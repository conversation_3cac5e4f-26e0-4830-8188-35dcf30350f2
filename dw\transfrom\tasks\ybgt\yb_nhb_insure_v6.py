import datetime
import json
import logging
import warnings
from pprint import pprint

import idna
import numpy as np
import pandas as pd
import pymysql
from django.core.cache import cache
from pandasql import sqldf

from dw import settings
from task.utils.cache_manager import CacheManager
from other.models import OtherYbStatisticNhb
from public.models import PublicMapping, PublicTarget, PublicStatistics
from insure.models import InsureArea, InsureOnline, InsureAgent
from transfrom.utils.utils import query_sql, match_type, sum_or_combine, df_to_dict, send_feishu_message

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.8f' % x)


class YbNhbInsureV6(CacheManager):
    """
    只算主险，
    团单个单中统计了保司上传的团单数据，但是其他没有统计团单
    """

    def __init__(self):
        super().__init__()
        self.sale_start_date = '2024-09-12'
        self.sale_start_datetime = '2024-09-12 16:00:00'
        self.sale_start_datetime_zero = '2024-09-12 00:00:00'
        self.sale_end_date_prev = '2023-12-31'
        # self.end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.end_datetime = '2025-01-24 23:59:59'
        # 24小时前的时间
        self.start_datetime = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
            hours=24)).strftime('%Y-%m-%d %H:%M:%S')
        # 今天的时间
        self.today = datetime.datetime.today().strftime('%Y-%m-%d')
        # 昨天的时间
        self.yesterday = (datetime.datetime.today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        self.today_time = self.today + ' 00:00:00'
        self.yesterday_time = self.yesterday + ' 00:00:00'
        self.product_set_code = 'ninghuibaoV5'  # 产品集编码
        self.product_set_code_prev = 'ninghuibaoV4'  # 上期产品集编码
        self.product_short_name_prev = '四期'
        self.DB = settings.DATABASES['jkx']  # 健康险数据库
        self.DB_DW = settings.DATABASES['default']  # dw数据数据库
        self.data_type = 'nhb_insure_v6'
        self.special_count = 0  # 中国人寿特殊订单
        self.target = 2000000  # 目标
        self.special_count_hk = 0  # 自动划扣的特殊单，预计节后会扣除额单量，基础版23769单，升级版85658单
        self.special_count_base = 0
        self.special_count_upgrade = 0

    def get_function_cache_key(self, func_name):
        return f'data_source:NhbInsureV5:{func_name}'

    def get_from_cache(self, method, timeout=3600 * 6, use_local=True):
        key = self.get_function_cache_key(method)

        if use_local:
            data = self.cached.get(key, None)
            if data is not None:
                return data

        data = cache.get(key)
        if data is not None:
            self.cached[key] = data
            return data
        else:
            logger.info(f'get_from_cache {key} miss')
            data = getattr(self, method)()
            self.cached[key] = data
            cache.set(key, data, timeout)
            return data

    # 在文件的顶部或适当位置添加
    def default_converter(self,o):
        if isinstance(o, int) or isinstance(o, np.int64):
            return int(o)
        raise TypeError(f'Object of type {o.__class__.__name__} is not JSON serializable')

    def update_or_create_db(self, product_code, content):
        """
        尝试获取指定product_code的记录
        :param product_code: 产品代码
        :param content: 内容
        """
        try:
            claim_yb_report = OtherYbStatisticNhb.objects.get(product_code=product_code, type=self.data_type)
            # 如果记录存在，则更新content，需要转成json格式，不然解析出来会是str
            claim_yb_report.content = json.dumps(content, default=self.default_converter)
            claim_yb_report.is_post = 0
            claim_yb_report.save()
        except OtherYbStatisticNhb.DoesNotExist:
            # 如果记录不存在，则新增记录，需要转成json格式，不然解析出来会是str
            OtherYbStatisticNhb.objects.create(product_code=product_code, type=self.data_type,
                                               content=json.dumps(content,default=self.default_converter))

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def get_connection_dw(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB_DW["HOST"]).decode('utf-8'), port=int(self.DB_DW["PORT"]),
                                    user=self.DB_DW["USER"],
                                    password=self.DB_DW["PASSWORD"], database=self.DB_DW["NAME"])
        return self.conn

    def get_daily_sale(self):
        """
        获取健康险日度销售数据，包括日期、医保类型、是否线上、是否个人、支付方式、区域编码、代理人、渠道来源、销售金额、销售件数、产品名称
        """
        try:
            df_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_DAILY_SALE').format(product_set_code=self.product_set_code,
                                                       end_datetime=self.end_datetime),
                self.get_connection())
            return df_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_daily_sale error:{e}')

    def get_main_daily_sale(self):
        """
        获取健康险日度销售数据，包括日期、医保类型、是否线上、是否个人、支付方式、区域编码、代理人、渠道来源、销售金额、销售件数、产品名称
        """
        try:
            df_main_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_MAIN_DATA').format(product_set_code=self.product_set_code,
                                                      end_datetime=self.end_datetime),
                self.get_connection())
            return df_main_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_main_daily_sale error:{e}')

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection_dw() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def adjust_data(self, df):
        """
        调整数据，将12-31（销售截止）之后的数据纳入之前的数据
        :param df: 需要处理的数据
        :return:
        """
        df_out_period = df[df['date'] > self.sale_end_date_prev]
        df_in_period = df[df['date'] <= self.sale_end_date_prev]
        if df_out_period.empty:
            df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
            df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
            return df_in_period
        else:
            # 计算销售期之前的销量每期的占比，再将销售期外的销量加到销售期内
            df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
            df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
            return df_in_period

    def online_adjust_data(self):
        """
        调整线上数据，将销售截止之后的数据纳入之前的数据，只计算个单。
        """
        # 获取上期的线上数据
        try:
            with self.get_connection_dw() as conn:
                sql = query_sql('SQL_DW_INDICATOR_DATA').format(
                    product_set_code=self.product_set_code_prev,
                    statistical_type='当期值', unit='单',
                    freq='日', start_datetime='2000-01-01',
                    end_datetime=self.end_datetime
                )
                df = pd.read_sql(sql, conn)
                df.rename(columns={'end_time': 'date'}, inplace=True)

                # 优化：只筛选一次，并使用映射来分配销售渠道
                channels = {
                    '我的南京': '-销量-线上-我的南京-当期值',
                    '公众号': '-销量-线上-公众号-当期值',
                    '支付宝': '-销量-线上-支付宝-当期值',
                    '合计': '-销量-线上-当期值'
                }

                df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
                for channel_name, filter_str in channels.items():
                    channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
                    channel_df['name'] = channel_name  # 添加销售渠道名称列
                    adjust_channel_df = self.adjust_data(channel_df)  # 调整数据
                    df_onlines = pd.concat([df_onlines, adjust_channel_df])  # 拼接数据
            df_onlines.sort_values(by=['name', 'date'], inplace=True)
            df_onlines.reset_index(drop=True, inplace=True)
            df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
        except Exception as e:
            print(f"Error occurred while processing online data: {e}")

        return df_onlines

    def offline_adjust_data(self):
        """
        调整线下数据，将12-31（销售截止）之后的数据纳入之前的数据，只计算个单
        :param df: 需要处理的数据
        :return:
        """
        # 提取SQL查询逻辑
        sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code_prev,
            statistical_type='当期值',
            unit='单',
            freq='日',
            start_datetime='2000-01-01',
            end_datetime=self.end_datetime
        )

        try:
            # 使用上下文管理器确保连接正确关闭
            with self.get_connection_dw() as conn:
                df = pd.read_sql(sql_query, conn)
                df.rename(columns={'end_time': 'date'}, inplace=True)

                # 定义公司名称列表
                company_names = [
                    ('-销量-线下-个单-中国人保-当期值', '中国人保'),
                    ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
                    ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
                    ('-销量-线下-个单-中华联合-当期值', '中华联合'),
                    ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
                    ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
                    ('-销量-线下-个单-太保产险-当期值', '太保产险'),
                    ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
                    ('-销量-线下-个单-中银保险-当期值', '中银保险'),
                    ('-销量-线下-个单-当期值', '合计')
                ]

                # 循环处理每个公司数据
                dfs = []
                for pattern, name in company_names:
                    filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
                    adjusted_df = self.adjust_data(filtered_df)
                    adjusted_df['name'] = name
                    dfs.append(adjusted_df)

                # 合并所有公司数据
                df_offlines = pd.concat(dfs)
                df_offlines.sort_values(by=['name', 'date'], inplace=True)
                df_offlines.reset_index(drop=True, inplace=True)
                df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
                return df_offlines

        except Exception as e:
            # 异常处理
            print(f"Error occurred while processing data: {e}")

    def online_daily_target(self):
        """
        计算线上每日目标
        """
        with self.get_connection_dw() as conn:
            sql = query_sql('SQL_DW_INDICATOR_DATA').format(
                product_set_code=self.product_set_code,
                statistical_type='当期值', unit='单',
                freq='日', start_datetime='2000-01-01',
                end_datetime=self.end_datetime
            )
            df = pd.read_sql(sql, conn)
            df.rename(columns={'end_time': 'date'}, inplace=True)

            # 优化：只筛选一次，并使用映射来分配销售渠道
            channels = {
                '我的南京': '-销量-线上-我的南京-当期值',
                '公众号': '-销量-线上-公众号-当期值',
                '支付宝': '-销量-线上-支付宝-当期值',
                '合计': '-销量-线上-当期值'
            }

            df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
            for channel_name, filter_str in channels.items():
                channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
                channel_df['name'] = channel_name  # 添加销售渠道名称列
                df_onlines = pd.concat([df_onlines, channel_df])  # 拼接数据
        df_onlines.sort_values(by=['name', 'date'], inplace=True)
        df_onlines.reset_index(drop=True, inplace=True)
        df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
        df_onlines['cumulative_count'] = df_onlines.groupby(['name'])['value'].cumsum()

        # 获取今年的目标数据，计算出目标的每日分布与每日累计分布
        target_online = PublicTarget.objects.filter(type='online', product_set_code=self.product_set_code).values(
            'name',
            'short_name',
            'target')
        df_target_online = pd.DataFrame(target_online)
        number_sum = df_target_online.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        # 保证合计在第一行
        df_target_online = pd.concat([number_sum, df_target_online], axis=0).reset_index(drop=True)
        # 获取去年的目标占比
        df_onlines_prev = self.online_adjust_data()
        df_onlines_prev = pd.merge(df_onlines_prev, df_target_online, on='name', how='left')
        df_onlines_prev['target_value_cumsum'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio_cumsum']
        df_onlines_prev['target_value'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio']
        df_onlines_prev = df_onlines_prev[['name', 'target', 'day', 'target_value', 'target_value_cumsum']]
        df_onlines_prev['target'] = df_onlines_prev['target'].astype(int)
        df_onlines_prev.reset_index(drop=True, inplace=True)
        df_onlines.reset_index(drop=True, inplace=True)
        df = sqldf(
            "select a.*,b.target_value_cumsum ,ifnull(b.target_value,0) target_value,b.target  from df_onlines a left join df_onlines_prev b on a.name=b.name and a.day=b.day")
        df['target_value_cumsum'] = df['target_value_cumsum'].fillna(method='ffill')
        # target 为空，根据name分组，取上一个值
        df['target'] = df['target'].fillna(method='ffill')
        # 根据name分组 取date最大的一条记录
        df_last_record = df.groupby(['name'])['date'].transform('max')
        df_last = df[df['date'] == df_last_record]
        df_last = df_last[['name', 'cumulative_count', 'target']].rename(
            columns={'cumulative_count': '实际', 'target': '目标'})
        # 创建空字典
        result_dict = {}

        # 遍历 DataFrame 并构建字典
        for index, row in df_last.iterrows():
            name = row["name"]
            actual = row["实际"]
            target = row["目标"]
            result_dict[name] = {"实际": actual, "目标": target}

        df_melt_cumsum = df.melt(id_vars=['name', 'day'], value_vars=['cumulative_count', 'target_value_cumsum'],
                                 var_name='type', value_name='count_cumsum').fillna(0)
        df_melt_value = df.melt(id_vars=['name', 'day'], value_vars=['value', 'target_value'],
                                var_name='type', value_name='count').fillna(0)
        df_melt_cumsum['type'] = df_melt_cumsum['type'].map({'cumulative_count': '实际', 'target_value_cumsum': '目标'})
        df_melt_value['type'] = df_melt_value['type'].map({'value': '实际', 'target_value': '目标'})

        df = pd.merge(df_melt_cumsum, df_melt_value, on=['name', 'day', 'type'], how='left')
        df.reset_index(drop=True, inplace=True)
        df.rename(columns={'day': 'x', 'type': 's', 'count': 'y', 'count_cumsum': 'sum'}, inplace=True)
        dict_online = df_to_dict(df, 'name', ['x', 's', 'y', 'sum'])
        dict_online['达成'] = result_dict
        df['sum'] = df['sum'].round(0)
        return df, dict_online

    def offline_daily_target(self):
        """
        计算线下每日目标
        """
        # 提取SQL查询逻辑
        sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code,
            statistical_type='当期值',
            unit='单',
            freq='日',
            start_datetime='2000-01-01',
            end_datetime=self.end_datetime
        )

        # 使用上下文管理器确保连接正确关闭
        with self.get_connection_dw() as conn:
            df = pd.read_sql(sql_query, conn)
            df.rename(columns={'end_time': 'date'}, inplace=True)

            # 定义公司名称列表
            company_names = [
                ('-销量-线下-个单-中国人保-当期值', '中国人保'),
                ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
                ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
                ('-销量-线下-个单-中华联合-当期值', '中华联合'),
                ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
                ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
                ('-销量-线下-个单-太保产险-当期值', '太保产险'),
                ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
                ('-销量-线下-个单-中银保险-当期值', '中银保险'),
                ('-销量-线下-个单-当期值', '合计')
            ]

            # 循环处理每个公司数据
            dfs = []
            for pattern, name in company_names:
                filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
                filtered_df['name'] = name
                dfs.append(filtered_df)

        # 合并所有公司数据
        df_offlines = pd.concat(dfs)
        df_offlines.sort_values(by=['name', 'date'], inplace=True)
        df_offlines.reset_index(drop=True, inplace=True)
        df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
        df_offlines['cumulative_count'] = df_offlines.groupby(['name'])['value'].cumsum()

        # 获取今年的目标数据，计算出目标的每日分布与每日累计分布
        target_offline = PublicTarget.objects.filter(type='agent', product_set_code=self.product_set_code).values(
            'name',
            'short_name',
            'target')
        df_target_offline = pd.DataFrame(target_offline)
        df_target_offline.rename(columns={'name': 'full_name', 'short_name': 'name'}, inplace=True)
        number_sum = df_target_offline.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        # 保证合计在第一行
        df_target_offline = pd.concat([number_sum, df_target_offline], axis=0).reset_index(drop=True)
        # 获取去年的目标占比
        df_offlines_prev = self.offline_adjust_data()
        df_offlines_prev = pd.merge(df_offlines_prev, df_target_offline, on='name', how='left')
        df_offlines_prev['target_value_cumsum'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio_cumsum']
        df_offlines_prev['target_value'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio']
        df_offlines_prev = df_offlines_prev[
            ['full_name', 'name', 'day', 'target', 'target_value', 'target_value_cumsum']]
        df_offlines_prev['target'] = df_offlines_prev['target'].astype(int)
        df_offlines_prev.reset_index(drop=True, inplace=True)
        df_offlines.reset_index(drop=True, inplace=True)
        df = sqldf(
            "select a.*,b.target_value_cumsum ,b.full_name,ifnull(b.target_value,0) target_value,b.target target from df_offlines a left join df_offlines_prev b on a.name=b.name and a.day=b.day")
        df['full_name'].fillna(df['name'], inplace=True)
        df['target_value_cumsum'] = df['target_value_cumsum'].fillna(method='ffill')
        # target 为空，根据name分组，取上一个值
        df['target'] = df['target'].fillna(method='ffill')
        # 根据name分组 取date最大的一条记录
        df_last_record = df.groupby(['name'])['date'].transform('max')
        df_last = df[df['date'] == df_last_record]
        df_last = df_last[['name', 'cumulative_count', 'target']].rename(
            columns={'cumulative_count': '实际', 'target': '目标'})
        # 创建空字典
        result_dict = {}

        # 遍历 DataFrame 并构建字典
        for index, row in df_last.iterrows():
            name = row["name"]
            actual = row["实际"]
            target = row["目标"]
            result_dict[name] = {"实际": actual, "目标": target}

        df_melt_cumsum = df.melt(id_vars=['full_name', 'name', 'day'],
                                 value_vars=['cumulative_count', 'target_value_cumsum'], var_name='type',
                                 value_name='count_cumsum').fillna(0)
        df_melt_value = df.melt(id_vars=['full_name', 'name', 'day'], value_vars=['value', 'target_value'],
                                var_name='type', value_name='count').fillna(0)
        df_melt_cumsum['type'] = df_melt_cumsum['type'].map({'cumulative_count': '实际', 'target_value_cumsum': '目标'})
        df_melt_value['type'] = df_melt_value['type'].map({'value': '实际', 'target_value': '目标'})

        df = pd.merge(df_melt_cumsum, df_melt_value, on=['full_name', 'name', 'day', 'type'], how='left')
        df.reset_index(drop=True, inplace=True)
        df.rename(columns={'day': 'x', 'type': 's', 'count': 'y', 'count_cumsum': 'sum', 'full_name': 'seller'},
                  inplace=True)
        df_total = df[df['seller'] == '合计']
        df_other = df[df['seller'] != '合计']
        dict_total = df_to_dict(df_total, 'name', ['x', 's', 'y', 'sum'])
        dict_other = df_to_dict(df_other, 'name', ['name', 'seller', 'x', 's', 'y', 'sum'])
        dict_offline = {**dict_total, **dict_other}
        dict_offline['达成'] = result_dict
        df['sum'] = df['sum'].round(0)
        return df, dict_offline

    def daily_target(self):
        df_online_target, data_online = self.online_daily_target()
        df_offline_target, data_offline = self.offline_daily_target()
        online_total = data_online['合计']
        offline_total = data_offline['合计']

        online_total = {
            '实际': [item for item in online_total if item['s'] == '实际'],
            '目标': [item for item in online_total if item['s'] == '目标'],
        }
        offline_total = {
            '实际': [item for item in offline_total if item['s'] == '实际'],
            '目标': [item for item in offline_total if item['s'] == '目标'],
        }

        total = {
            '实际': [],
            '目标': [],
        }

        for index in range(len(online_total['实际'])):
            total['实际'].append({
                'x': online_total['实际'][index]['x'],
                'y': online_total['实际'][index]['y'] + offline_total['实际'][index]['y'],
                'sum': online_total['实际'][index]['sum'] + offline_total['实际'][index]['sum'],
                's': '实际'
            })

        for index in range(len(online_total['目标'])):
            total['目标'].append({
                'x': online_total['目标'][index]['x'],
                'y': online_total['目标'][index]['y'] + offline_total['目标'][index]['y'],
                'sum': online_total['目标'][index]['sum'] + offline_total['目标'][index]['sum'],
                's': '目标'
            })

        data_total = []
        data_total.extend(total['实际'])
        data_total.extend(total['目标'])
        data_total.sort(key=lambda x: x['x'])

        data = {
            '合计': data_total,
            '线上': data_online,
            '线下': data_offline
        }
        return data

    def get_insure_person(self):
        """
        获取参保人数，只有主险
        :param product_set_code:产品集编码
        :param end_datetime:截止日期
        :return:参保人数
        """
        sql = query_sql('SQL_INSURE_PERSON_MAIN')
        sql += " and o.is_personal = 1"
        with self.get_connection() as conn:
            df_insure_person = pd.read_sql(
                sql.format(product_set_code=self.product_set_code,
                                                           end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                conn)
            total_insure_person = int(df_insure_person['person_num'].sum())
            print(total_insure_person)
            # 手动上传的团单数据
            df_offline_seller_group = self.get_seller_group_report_data()
            group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
            group_seller.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
            # group_seller = pd.DataFrame(self.SELLERS)
            group_seller_sum = int(group_seller['group_count'].sum())
            print(group_seller_sum)
            total_insure_person += group_seller_sum
        return total_insure_person

    def get_insure_personal_amount(self):
        """
        获取个单累计保费，历史逻辑直接取数据库的金额汇总，保持一致
        如果严格的个单，需要限制是否团单是1
        """
        df = self.get_from_cache('get_main_daily_sale')
        df = df[df['main'] == 1]
        # total_personal_amount = df[df['is_personal'] == 1]['amount'].sum()
        total_personal_amount = df['amount'].sum()
        total_personal_amount = int(total_personal_amount)
        return total_personal_amount

    def get_renewal_rate(self):
        """
        获取续保率
        :return:
        """
        with self.get_connection_dw() as conn:
            df_renewal_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_renewal_rate = df_renewal_rate[df_renewal_rate['name'].str.contains('续保占比-当期值')][['value']]
            if len(df_renewal_rate) > 0:
                renewal_rate = float(df_renewal_rate.iloc[0]['value'] / 100)
                renewal_rate = '{:.7f}'.format(renewal_rate)
            else:
                renewal_rate = 0
        return renewal_rate

    def get_complete_rate(self):
        """
        获取完成率
        :return:
        """
        with self.get_connection_dw() as conn:
            df_complete_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_complete_rate = df_complete_rate[df_complete_rate['name'].str.contains('完成率-当期值')][['value']]
            if len(df_complete_rate) > 0:
                complete_rate = float(df_complete_rate.iloc[0]['value'] / 100)
                complete_rate = '{:.7f}'.format(complete_rate)  # 保留7位小数
            else:
                complete_rate = 0
        return complete_rate

    def get_target_accumulate_ratio(self):
        """
        获取目标达成率，根据历史逻辑为个单完成的单量/预计的目标数量
        """
        target_data = self.daily_target()['合计']
        # today = datetime.date.today()
        today = datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S').date()
        sumed = [item for item in target_data if item['x'] == today.strftime('%m-%d')]
        target_accumulate = [item['sum'] for item in sumed if item['s'] == '目标'][0]
        count_accumulate = [item['sum'] for item in sumed if item['s'] == '实际'][0]
        target_accumulate_ratio = round(count_accumulate / target_accumulate, 4) if target_accumulate else 0
        return target_accumulate_ratio

    def get_pay_amount(self):
        """
        获取不同支付类型金额，自费、个账
        只取了主险的个账和自费
        """
        pay_amount = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                     statistical_type='pay_amount_personal').values('key', 'value')
        df_pay_amount = pd.DataFrame(pay_amount)
        if '个账' in df_pay_amount['key'].tolist():
            medicare_pay = int(df_pay_amount[df_pay_amount['key'] == '个账']['value'].sum())
        else:
            medicare_pay = 0

        if '自费' in df_pay_amount['key'].tolist():
            self_pay = int(df_pay_amount[df_pay_amount['key'] == '自费']['value'].sum())
        else:
            self_pay = 0
        return medicare_pay, self_pay

    def get_pay_count(self):
        """
        获取不同支付类型数量，自费、个账
        只取了主险的个账和自费
        """
        pay_amount = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                     statistical_type='pay_personal').values('key', 'value')
        df_pay = pd.DataFrame(pay_amount)
        if df_pay.empty:
            df_pay = pd.DataFrame({'key': ['个账', '自费'], 'value': [0, 0]})
        df_pay.rename(columns={'key': 'name'}, inplace=True)
        df = pd.DataFrame({'name': ['个账', '自费']})
        df_pay = pd.merge(df, df_pay, on='name', how='left')
        df_pay['value'] = df_pay['value'].fillna(0)
        df_pay['value'] = df_pay['value'].astype('int')
        dict_pay = df_pay.to_dict(orient='records')
        return dict_pay

    def get_personal_group_count(self):
        """
        获取个单、团单数量分布
        """
        pay_amount = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                     statistical_type='personal').values('key', 'value')
        df = pd.DataFrame(pay_amount)
        if df.empty:
            df = pd.DataFrame({'key': ['个单', '团单'], 'value': [0, 0]})
        df['value'] = df['value'].astype('int')

        if '个单' in df['key'].tolist():
            personal_count = int(df[df['key'] == '个单']['value'].sum())
        else:
            personal_count = 0

        if '团单' in df['key'].tolist():
            group_count = int(df[df['key'] == '团单']['value'].sum())
        else:
            group_count = 0
        df.rename(columns={'key': 'name'}, inplace=True)

        df_name = pd.DataFrame({'name': ['个单', '团单']})
        df = pd.merge(df_name, df, on='name', how='left')
        df['value'] = df['value'].fillna(0)
        dict_personal_group_count = df.to_dict(orient='records')
        return personal_count, group_count, dict_personal_group_count

    def get_isonline_count(self):
        """
        获取线上、线下数量分布 只取个单
        """
        pay_amount = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                     statistical_type='isonline_personal').values('key', 'value')
        df = pd.DataFrame(pay_amount)
        if df.empty:
            df = pd.DataFrame({'key': ['线上', '线下'], 'value': [0, 0]})
        df['value'] = df['value'].astype('int')
        if '线上' in df['key'].tolist():
            online_count = int(df[df['key'] == '线上']['value'].sum())
        else:
            online_count = 0

        if '线下' in df['key'].tolist():
            offline_count = int(df[df['key'] == '线下']['value'].sum())
        else:
            offline_count = 0
        df.rename(columns={'key': 'name'}, inplace=True)
        df_name = pd.DataFrame({'name': ['线上', '线下']})
        df = pd.merge(df_name, df, on='name', how='left')
        df['value'] = df['value'].fillna(0)
        dict_isonline_count = df.to_dict(orient='records')
        return online_count, offline_count, dict_isonline_count

    def get_product_count(self):
        """
        获取基础版、升级版数量分布
        """
        pay_amount = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                     statistical_type='product_personal').values('key', 'value')
        df = pd.DataFrame(pay_amount)
        if df.empty:
            df = pd.DataFrame({'key': ['基础版', '升级版'], 'value': [0, 0]})
        df['value'] = df['value'].astype('int')
        if '基础版' in df['key'].tolist():
            base_count = int(df[df['key'] == '基础版']['value'].sum())
        else:
            base_count = 0

        if '升级版' in df['key'].tolist():
            upgrade_count = int(df[df['key'] == '升级版']['value'].sum())
        else:
            upgrade_count = 0

        df.rename(columns={'key': 'name'}, inplace=True)
        df_name = pd.DataFrame({'name': ['基础版', '升级版']})
        df = pd.merge(df_name, df, on='name', how='left')
        df['value'] = df['value'].fillna(0)
        dict_product_count = df.to_dict(orient='records')
        return base_count, upgrade_count, dict_product_count

    def get_product_amount(self):
        """
        获取基础版、升级版金额分布，只有主险
        """
        pay_amount = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                     statistical_type='product_amount_personal').values('key', 'value')
        df = pd.DataFrame(pay_amount)
        if df.empty:
            df = pd.DataFrame({'key': ['基础版', '升级版'], 'value': [0, 0]})
        if '基础版' in df['key'].tolist():
            base_amount = int(df[df['key'] == '基础版']['value'].sum())
        else:
            base_amount = 0

        if '升级版' in df['key'].tolist():
            upgrade_amount = int(df[df['key'] == '升级版']['value'].sum())
        else:
            upgrade_amount = 0
        return base_amount, upgrade_amount

    def get_today_sale(self):
        """
        获取当日数据，单量是主险、金额是全部险种累计，包括附加险
        """
        with self.get_connection_dw() as conn:
            # 今日参保
            df_today_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.today_time), conn)
            df_today_count = df_today_count[
                df_today_count['name'].str.contains('-销量-当期值') & ~df_today_count[
                    'name'].str.contains('小时')][['value']]
            if df_today_count.empty:
                df_today_count = pd.DataFrame({'value': [0]})
            today_count = df_today_count.iloc[0]['value']

            # 今日保费
            df_today_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.today_time), conn)
            df_today_amount = df_today_amount[
                df_today_amount['name'].str.contains('-销售额-当期值') & ~df_today_amount[
                    'name'].str.contains('小时')][['value']]
            if df_today_amount.empty:
                df_today_amount = pd.DataFrame({'value': [0]})
            today_amount = df_today_amount.iloc[0]['value']
        return today_count, today_amount

    def get_yesterday_sale(self):
        """
        获取昨日数据，单量是主险、金额是全部险种累计，包括附加险
        """
        with self.get_connection_dw() as conn:
            # 今日参保
            df_yesterday_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.yesterday_time), conn)
            df_yesterday_count = df_yesterday_count[
                df_yesterday_count['name'].str.contains('-销量-当期值') & ~df_yesterday_count[
                    'name'].str.contains('小时')][['value']]
            if df_yesterday_count.empty:
                df_yesterday_count = pd.DataFrame({'value': [0]})
            yesterday_count = df_yesterday_count.iloc[0]['value']

            # 今日保费
            df_yesterday_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.yesterday_time), conn)
            df_yesterday_amount = df_yesterday_amount[
                df_yesterday_amount['name'].str.contains('-销售额-当期值') & ~df_yesterday_amount[
                    'name'].str.contains('小时')][['value']]
            if df_yesterday_amount.empty:
                df_yesterday_amount = pd.DataFrame({'value': [0]})
            yesterday_amount = df_yesterday_amount.iloc[0]['value']
        return yesterday_count, yesterday_amount

    def get_last_24_hour_data(self):
        """
        最近24小时销量数据
        """
        start_datetime = max(
            datetime.datetime.strptime(self.start_datetime, '%Y-%m-%d %H:%M:%S'),
            datetime.datetime.strptime(self.sale_start_datetime, '%Y-%m-%d %H:%M:%S'))
        with self.get_connection_dw() as conn:
            df_last_24_hour_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='小时', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_24_hour_data = df_last_24_hour_data[df_last_24_hour_data['name'].str.contains('销量-当期值')][
                ['end_time', 'value']]
            df_last_24_hour_data['end_time'] = df_last_24_hour_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data['value'] = df_last_24_hour_data['value'].astype(int)
            df_last_24_hour_data.rename(columns={'end_time': 'x'}, inplace=True)
            dict_last_24_hour_data = df_last_24_hour_data.to_dict(orient='records')
            return dict_last_24_hour_data

    def get_area_data(self):
        """
        参保地统计报表（排名、参保地、占比、总单数、今日参保、昨日参保、参保率）
        """

        area_data = InsureArea.objects.filter(product_set_code=self.product_set_code)
        df_area_data = pd.DataFrame(list(
            area_data.values('position', 'name', 'ratio', 'total_count', 'today_count', 'yesterday_count',
                             'insure_ratio')))
        # 按照排序先确定顺序，并对合计的顺序置为-
        if df_area_data.empty:
            df_area_data = pd.DataFrame()
        else:
            df_area_data.sort_values(by='position', inplace=True)
            df_area_data.reset_index(inplace=True)
            df_area_data[['insure_ratio', 'ratio']] = df_area_data[['insure_ratio', 'ratio']].astype(float)
            df_area_data.loc[df_area_data['name'] == '合计', 'position'] = '-'
            # 如果name中有其他，insure_ratio置为""
            df_area_data.loc[df_area_data['name'] == '其他', 'insure_ratio'] = ''
            df_area_data.rename(columns={'position': 'index'}, inplace=True)
        dict_area_data = df_area_data.to_dict(orient='records')
        return dict_area_data

    def get_online_data(self):
        """
        线上统计报表(排名、渠道、占比、总单数、今日参保、昨日参保、目标、完成率)
        """
        online_data = InsureOnline.objects.filter(product_set_code=self.product_set_code)
        df_online_data = pd.DataFrame(list(
            online_data.values('position', 'channel_name', 'insure_ratio', 'total_count', 'today_count',
                               'yesterday_count', 'target', 'target_ratio')))
        if df_online_data.empty:
            df_online_data = pd.DataFrame()
        else:
            # 按照排序先确定顺序，并对合计的顺序置为-
            df_online_data.sort_values(by=['position'], inplace=True)
            df_online_data.reset_index(inplace=True)
            df_online_data['position'] = df_online_data.index + 1
            df_online_data[['insure_ratio', 'target_ratio']] = df_online_data[['insure_ratio', 'target_ratio']].astype(
                float)
            df_online_data.loc[df_online_data['channel_name'] == '合计', 'position'] = '-'
            df_online_data.rename(columns={'position': 'index', 'channel_name': 'name', 'insure_ratio': 'ratio'},
                                  inplace=True)
            online_daily_target, _ = self.online_daily_target()
            today_online_cumsum_target = online_daily_target[online_daily_target['s'] == '目标']
            # 获取当日的目标
            today_month_day = datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S').strftime('%m-%d')
            today_online_cumsum_target = today_online_cumsum_target[today_online_cumsum_target['x'] == today_month_day]
            today_online_cumsum_target.rename(columns={'sum': 'target_accumulate'}, inplace=True)
            df_online_data = df_online_data.merge(today_online_cumsum_target[['name', 'target_accumulate']], on='name',
                                                  how='left')
            # 如果目标大于0，则正常计算，否则为1，也就是说当日没有目标，完成多少都是1
            df_online_data['target_accumulate_ratio'] = df_online_data.apply(
                lambda x: round(x['total_count'] / x['target_accumulate'], 3) if x['target_accumulate'] > 0 else 1,
                axis=1)
            df_online_data['target_accumulate'] = df_online_data['target_accumulate'].astype(int)
        dict_online_data = df_online_data.to_dict(orient='records')
        return dict_online_data

    def get_agent_data(self):
        """
        获取线下保司销售数据（排名、保司、占比、代理人数、人均出单、个单、团单、目标、完成率）
        """
        agent_data = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        df_agent_data = pd.DataFrame(list(agent_data.values('position', 'name', 'insure_ratio', 'employee_count',
                                                            'average_count', 'personal_count', 'group_count', 'target',
                                                            'target_ratio', 'today_count', 'yesterday_count')))
        if df_agent_data.empty:
            df_agent_data = pd.DataFrame()
        else:
            # 按照排序先确定顺序，并对合计的顺序置为-
            df_agent_data.sort_values(by=['position'], inplace=True)
            df_agent_data[['insure_ratio', 'target_ratio', 'average_count']] = df_agent_data[
                ['insure_ratio', 'target_ratio', 'average_count']].astype(
                float)
            df_agent_data.loc[df_agent_data['name'] == '合计', 'position'] = '-'
            df_agent_data['average_count'] = df_agent_data['average_count'].round(0).astype(int)
            # 个单名称调整为总单数，保持历史一致
            df_agent_data.rename(
                columns={'position': 'index', 'insure_ratio': 'ratio', 'employee_count': 'agent_number',
                         'average_count': 'agent_average_count', 'personal_count': 'total_count'},
                inplace=True)

            offline_daily_target, _ = self.offline_daily_target()
            today_offline_cumsum_target = offline_daily_target[offline_daily_target['s'] == '目标']
            # 获取当日的目标
            today_month_day = datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S').strftime('%m-%d')
            today_offline_cumsum_target = today_offline_cumsum_target[
                today_offline_cumsum_target['x'] == today_month_day]
            today_offline_cumsum_target.rename(columns={'sum': 'target_accumulate'}, inplace=True)
            df_agent_data = df_agent_data.merge(today_offline_cumsum_target[['name', 'target_accumulate']], on='name',
                                                how='left')
            # 如果目标大于0，则正常计算，否则为1，也就是说当日没有目标，完成多少都是1
            df_agent_data['target_accumulate_ratio'] = df_agent_data.apply(
                lambda x: round(x['total_count'] / x['target_accumulate'], 3) if x['target_accumulate'] > 0 else 1,
                axis=1)
            df_agent_data = df_agent_data[~df_agent_data['name'].str.contains('线上渠道')]
            df_agent_data['target_accumulate'] = df_agent_data['target_accumulate'].astype(int)
            # 特殊处理，开始的时候要处理
            df_agent_data['group_count'] = df_agent_data['group_count'].astype(str)
            # 如果是中国人寿，sepcial_count 不为0，则原来的字符串拼接 + sepcial_count
            df_agent_data['group_count'] = df_agent_data.apply(
                lambda x: x['group_count'] if x['name'] != '中国人寿' else str(x['group_count']) + "\n+\n" + str(
                    self.special_count),
                axis=1)
        dict_agent_data = df_agent_data.to_dict(orient='records')
        return dict_agent_data

    def get_place_count(self):
        """
        获取不同属地的单量、本地、异地
        只取了主险的个账和自费
        """
        place_count = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                      statistical_type='place_personal').values('key', 'value')
        df_place = pd.DataFrame(place_count)
        if df_place.empty:
            df_place = pd.DataFrame({'key': ['本地', '异地'], 'value': [0, 0]})
        df_place.rename(columns={'key': 'name'}, inplace=True)
        df = pd.DataFrame({'name': ['本地', '异地']})
        df_place = pd.merge(df, df_place, on='name', how='left')
        df_place['value'] = df_place['value'].fillna(0)
        df_place['value'] = df_place['value'].astype('int')
        dict_place = df_place.to_dict(orient='records')
        return dict_place

    def get_medicare_count(self):
        """
        获取不同险种的单量、职保、居保
        只取了主险的个账和自费
        """
        place_count = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type='insure',
                                                      statistical_type='medicare_personal').values('key', 'value')
        df_medicare = pd.DataFrame(place_count)
        if df_medicare.empty:
            df_medicare = pd.DataFrame({'key': ['职保', '居保'], 'value': [0, 0]})
        df_medicare.rename(columns={'key': 'name'}, inplace=True)
        df_medicare = df_medicare[df_medicare['name'] != '其他']
        df = pd.DataFrame({'name': ['职保', '居保']})
        df_medicare = pd.merge(df, df_medicare, on='name', how='left')
        df_medicare['value'] = df_medicare['value'].fillna(0)
        df_medicare['value'] = df_medicare['value'].astype('int')
        dict_medicare = df_medicare.to_dict(orient='records')
        return dict_medicare

    def get_compare_data(self):
        """
        获取本期个单与上期个单的对比数据
        """
        with self.get_connection_dw() as conn:
            df = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=self.sale_start_datetime_zero,
                                                          end_datetime=self.end_datetime), conn)
            df_offline = df[df['name'].str.contains('销量-线下-个单-当期值')][['name', 'end_time', 'value']]
            df_offline['name'] = df_offline['name'].apply(lambda x: x.split('-')[1])
            df_online = df[df['name'].str.contains('销量-线上-当期值')][['name', 'end_time', 'value']]
            df_online['name'] = df_online['name'].apply(lambda x: x.split('-')[1])
            df_total = pd.concat([df_offline, df_online], axis=0)
            df_total = df_total.groupby(['name', 'end_time'])['value'].sum().reset_index()
            df_total['x'] = df_total['end_time'].apply(lambda x: x.strftime('%m-%d'))
            df_total['type'] = '合计'
            df_total.rename(columns={'name': 's', 'value': 'y'}, inplace=True)

            # 上期数据
            df_prev = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code_prev,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime='2000-01-01 00:00:00',
                                                          end_datetime=self.end_datetime), conn)
            df_offline_prev = df_prev[df_prev['name'].str.contains('销量-线下-个单-当期值')][
                ['name', 'end_time', 'value']]
            df_offline_prev['name'] = df_offline_prev['name'].apply(lambda x: x.split('-')[1])
            df_online_prev = df_prev[df_prev['name'].str.contains('销量-线上-当期值')][['name', 'end_time', 'value']]
            df_online_prev['name'] = df_online_prev['name'].apply(lambda x: x.split('-')[1])
            df_total_prev = pd.concat([df_offline_prev, df_online_prev], axis=0)
            df_total_prev = df_total_prev.groupby(['name', 'end_time'])['value'].sum().reset_index()
            df_total_prev['x'] = df_total_prev['end_time'].apply(lambda x: x.strftime('%m-%d'))
            df_total_prev['type'] = '合计'
            df_total_prev.rename(columns={'name': 's', 'value': 'y'}, inplace=True)

            # 如果有日期在df_total中存在，在df_total_prev中不存在，则为新增数据，需要填充0
            df_all = sqldf(
                "select a.*,b.s s1,ifnull(b.y,0) y1,ifnull(b.x,a.x) x1,ifnull(b.type,'合计') type1 from df_total a left join df_total_prev b on a.x = b.x")
            df_all['s1'].fillna(self.product_short_name_prev, inplace=True)
            df_compare = pd.concat([df_all[['type', 's', 'x', 'y']], df_all[['type1', 's1', 'x1', 'y1']].rename(
                columns={'s1': 's', 'x1': 'x', 'y1': 'y', 'type1': 'type'})])
            df_compare['y'] = df_compare['y'].astype(int)
            df_compare.reset_index(drop=True, inplace=True)
            dict_compare = df_compare.to_dict(orient='records')
            return dict_compare

    def get_content(self):
        # 计算数据
        data = {}
        data['总参保人数'] = self.get_insure_person() + self.special_count + self.special_count_hk
        data['总续保率'] = self.get_renewal_rate()
        data['目标进度'] = round((self.get_insure_person() + self.special_count + self.special_count_hk) / self.target,
                                 7)
        data['目标达成率'] = self.get_target_accumulate_ratio()
        medicare_pay, self_pay = self.get_pay_amount()
        data['个账参保金额'] = medicare_pay + self.special_count_base * 99 + self.special_count_upgrade * 150
        data['自费参保金额'] = self_pay
        personal_count, group_count, dict_personal_group_count = self.get_personal_group_count()
        data['个单参保人数'] = personal_count + self.special_count_hk
        data['团单参保人数'] = group_count
        online_count, offline_count, dict_isonline_count = self.get_isonline_count()
        data['线上参保人数'] = online_count + self.special_count_hk
        data['线下参保人数'] = offline_count
        base_count, upgrade_count, dict_product_count = self.get_product_count()
        data['基础版参保人数'] = base_count + self.special_count_base
        data['升级版参保人数'] = upgrade_count + self.special_count_upgrade
        base_amount, upgrade_amount = self.get_product_amount()
        data['基础版保费'] = base_amount + self.special_count_base * 99
        data['升级版保费'] = upgrade_amount + self.special_count_upgrade * 150
        data[
            '个单累计保费'] = base_amount + upgrade_amount + self.special_count_base * 99 + self.special_count_upgrade * 150
        today_count, today_amount = self.get_today_sale()
        data['今日参保人数'] = today_count
        data['今日金额'] = today_amount
        yesterday_count, yesterday_amount = self.get_yesterday_sale()
        data['昨日参保人数'] = yesterday_count
        data['昨日参保金额'] = yesterday_amount
        data['24小时趋势'] = self.get_last_24_hour_data()
        data['各区概况'] = self.get_area_data()
        data['线上销售情况'] = self.get_online_data()
        data['线下销售情况'] = self.get_agent_data()
        data['参保分析_个单团单'] = dict_personal_group_count
        data['参保分析_个账自费'] = self.get_pay_count()
        data['参保分析_线上线下'] = dict_isonline_count
        data['参保分析_升级版基础版'] = dict_product_count
        data['参保分析_属地分析'] = self.get_place_count()
        data['参保分析_险种分析'] = self.get_medicare_count()
        data['销量对比'] = self.get_compare_data()
        self.update_or_create_db(self.product_set_code, data)
        logger.info(f"ybgt_nhb_insure_v5 get_content 数据已更新")
        return data


if __name__ == '__main__':
    source = YbNhbInsureV6()
    # df = source.get_from_cache('get_daily_sale')
    # print(df)
    # df_insure_person = source.get_insure_person()
    # print(df_insure_person)
    # df = source.get_target_accumulate_ratio()
    # print(df)
    get_content = source.get_content()
    pprint(get_content)
