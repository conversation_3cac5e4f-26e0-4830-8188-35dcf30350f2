import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)

import datetime
import logging
import time
import warnings
from decimal import Decimal

import idna
import numpy as np
import pandas as pd
import pymysql
import requests
from django.db import transaction
from pandasql import sqldf

from dw import settings
from insure.models import InsureArea, InsureAgeSex
from public.models import (
    PublicIndicatorData,
    PublicIndicatorMain,
    PublicStatistics,
    PublicTarget,
    SystemDictValue,
    PublicAreaBaseInsure,
)
from task.utils.cache_manager import CacheManager
from transfrom.utils.utils import (
    simplify_replace,
    query_sql,
    sum_or_combine,
    age_group,
    custom_update_or_create,
    query_indicator_code,
)

logger = logging.getLogger(__name__)
warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", 200)
pd.set_option("display.expand_frame_repr", False)
pd.set_option("display.float_format", lambda x: "%.6f" % x)


class BzHxbInsureV3(CacheManager):
    def __init__(self):
        super().__init__()
        self.DB = settings.DATABASES["jkx"]  # 销售数据数据库
        self.product_set_code = "binzhou_studentV3"  # 产品集编码
        self.city = "滨州"  # 城市
        self.version = "滨州护学保-三期"  # 产品期，用于指标名称标准化
        self.type = "insure"  # 统计大类
        self.sale_start_time = "2025-08-15 00:00:00"  # 销售起始时间

        self.sale_start_date = datetime.datetime.strptime(
            self.sale_start_time, "%Y-%m-%d %H:%M:%S"
        ).date()  # 销售起始日期
        self.end_time = datetime.datetime.now().strftime("%Y-%m-%d 00:00:00")
        self.today = datetime.datetime.strptime(self.end_time, "%Y-%m-%d %H:%M:%S")
        self.yesterday = datetime.datetime.strptime(
            self.end_time, "%Y-%m-%d %H:%M:%S"
        ) - datetime.timedelta(days=1)
        self.publish_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def get_connection(self):
        self.conn = pymysql.connect(
            host=idna.encode(self.DB["HOST"]).decode("utf-8"),
            port=int(self.DB["PORT"]),
            user=self.DB["USER"],
            password=self.DB["PASSWORD"],
            database=self.DB["NAME"],
        )
        return self.conn

    def get_full_range_date(self, df):
        """
        获取完整的日期范围
        """
        # 获取最小日期
        min_date = df["date"].min()
        # 如果 min_date 是空的，使用 sale_start_date 作为默认值
        min_date = min_date if pd.notnull(min_date) else self.sale_start_date
        # 如果 min_date 是 pandas.Timestamp 类型，转换为 datetime.date
        if isinstance(min_date, pd.Timestamp):
            min_date = min_date.to_pydatetime().date()
        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = pd.date_range(
            start=min(min_date, self.sale_start_date), end=self.today.date()
        )
        return full_date_range

    def get_all_product(self):
        """
        获取所有产品信息
        """
        with self.get_connection() as conn:
            df_all_product = pd.read_sql(
                query_sql("SQL_SALE_PRODUCT_INFO").format(
                    product_set_code=self.product_set_code
                ),
                conn,
            )
        return df_all_product

    def cache_all_product(self):
        """
        从数据库表中获取所有产品信息，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        由于多次从数据库读取数据，访问频率过高，存在访问失败的可能，所以增加缓存
        """
        try:
            _start = time.time()
            df_all_product = self.get_all_product()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache("get_all_product", df_all_product)
            _cost = int((time.time() - _start) * 1000)
            logger.info("BzHxbInsureV3 cache_all_product cost:{}ms".format(_cost))
            return df_all_product
        except Exception as e:
            logger.error("BzHxbInsureV3 cache_all_product error:{}".format(e))
            raise ValueError("BzHxbInsureV3 cache_all_product error:{}".format(e))

    def get_daily_sale(self):
        """
        获取日度销售数据
        如果不考虑数据落地dw，可以直接调整下面的sql，加入区域条件，再通过一个聚合所有数据的函数返回结果
        落地dw后，数据访问压力都在中间库（数据量相对少），可以减少源库的数据压力
        """
        with self.get_connection() as conn:
            df_daily_sale = pd.read_sql(
                query_sql("SQL_INSURE_DAILY_SALE_STUDENT").format(
                    product_set_code=self.product_set_code,
                    end_datetime=self.publish_time,
                ),
                conn,
            )
            # 地区合并
            df_daily_sale.loc[df_daily_sale["area_name"] == "北海新区", "area_code"] = (
                "999003"
            )
            df_daily_sale.loc[df_daily_sale["area_name"] == "市属", "area_code"] = (
                "999000"
            )
            df_daily_sale.loc[df_daily_sale["area_name"] == "市直", "area_code"] = (
                "999000"
            )
            df_daily_sale.loc[df_daily_sale["area_name"] == "市辖区", "area_code"] = (
                "999000"
            )
            df_daily_sale.loc[df_daily_sale["area_name"] == "开发区", "area_code"] = (
                "999001"
            )
            df_daily_sale.loc[df_daily_sale["area_name"] == "高新区", "area_code"] = (
                "999002"
            )
            df_daily_sale.loc[df_daily_sale["area_code"] == "999000", "area_name"] = (
                "市直"
            )
            # 有没有地区、或者学校的情况
            df_daily_sale.fillna(
                {
                    "area_code": "999999",
                    "area_name": "其他",
                    "district_name": "其他",
                    "school_name": "其他",
                },
                inplace=True,
            )

        return df_daily_sale

    def cache_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        try:
            _start = time.time()
            df_daily_sale = self.get_daily_sale()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache("get_daily_sale", df_daily_sale)
            _cost = int((time.time() - _start) * 1000)
            logger.info("BzHxbInsureV3 cache_daily_sale cost:{}ms".format(_cost))
            return df_daily_sale
        except Exception as e:
            logger.error("BzHxbInsureV3 cache_daily_sale error:{}".format(e))
            raise ValueError("BzHxbInsureV3 cache_daily_sale error:{}".format(e))

    def get_total_person_cumsum(self):
        """
        获取总参保人数
        """
        data = self.get_from_cache("get_daily_sale")
        total_count = str(data["person_count"].sum())
        indic_name = self.version + "-人数-累计值"
        df_total_cumsum = pd.DataFrame(
            {"indic_name": [indic_name], "person_count": [total_count]}
        )
        data_district = data.groupby(['area_name']).agg({'person_count': 'sum'}).reset_index()
        # 分区域销量累计值
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['area_name']).agg({'person_count': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.person_count from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-人数-' + x + '-累计值')
        df_total_cumsum = pd.concat([df_total_cumsum, data_district[['indic_name', 'person_count']]])
        df_total_cumsum.reset_index(drop=True, inplace=True)
        df_total_cumsum["code"] = None
        # 获取code
        query_indicator_code(df_total_cumsum)
        # 删除code为空的行
        df_total_cumsum.dropna(subset=["code"], inplace=True)
        df_total_cumsum.fillna(0, inplace=True)
        # 写入数据库
        try:
            with transaction.atomic():
                for index, row in df_total_cumsum.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.end_time,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["person_count"])),
                        },
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_total_person_cumsum error:{}".format(e))
        return df_total_cumsum

    def get_today_person_count(self):
        """
        获取当日参保人数
        """
        data = self.get_from_cache("get_daily_sale")
        today_count = Decimal(
            str(data[data["date"] == self.today.date()]["person_count"].sum())
        )
        indic_name = self.version + "-人数-当期值"
        df_today_count = pd.DataFrame(
            {"indic_name": [indic_name], "person_count": [today_count]}
        )
        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data[data['date'] == self.today.date()].groupby(['area_name']).agg(
            {'person_count': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.person_count from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-人数-' + x + '-当期值')
        df_today_count = pd.concat([df_today_count, data_district[['indic_name', 'person_count']]])
        df_today_count.reset_index(drop=True, inplace=True)
        df_today_count["code"] = None

        # 获取code
        query_indicator_code(df_today_count)
        # 删除code为空的行
        df_today_count.dropna(subset=["code"], inplace=True)
        df_today_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_today_count.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.end_time,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["person_count"])),
                        },
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_today_person_count error:{}".format(e))
        return df_today_count

    def get_yesterday_person_count(self):
        """
        获取昨日参保人数
        """
        # 检查 self.yesterday 是否小于 self.sale_start_date
        if self.yesterday.date() < self.sale_start_date:
            # 如果是，则返回空
            return None

        indic_name = self.version + "-人数-当期值"
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        data = self.get_from_cache("get_daily_sale")
        data.fillna({"area_name": "其他"}, inplace=True)
        data["date"] = pd.to_datetime(data["date"])
        yesterday_count = Decimal(
            str(data[data["date"] == self.yesterday]["person_count"].sum())
        )
        df_yesterday_count = pd.DataFrame(
            {"indic_name": [indic_name], "person_count": [yesterday_count]}
        )
        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data[data['date'] == self.yesterday].groupby(['area_name']).agg(
            {'person_count': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.person_count from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-当期值')
        df_yesterday_count = pd.concat([df_yesterday_count, data_district[['indic_name', 'person_count']]])
        df_yesterday_count.reset_index(drop=True, inplace=True)
        df_yesterday_count["code"] = None

        # 获取code
        query_indicator_code(df_yesterday_count)

        # 删除code为空的行
        df_yesterday_count.dropna(subset=["code"], inplace=True)
        df_yesterday_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_yesterday_count.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.yesterday,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["person_count"])),
                        },
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_yesterday_person_count error:{}".format(e))
        return df_yesterday_count

    def get_daily_person_cumsum(self):
        """
        获取日度累计参保人数，时间序列，用于更新历史数据
        """
        data = self.get_from_cache("get_daily_sale")
        df_daily_cumsum = (
            data.groupby(["date"]).agg({"person_count": "sum"}).reset_index()
        )
        df_daily_cumsum.sort_values(by="date", ascending=True, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum["cumulative_count"] = df_daily_cumsum["person_count"].cumsum()
        df_daily_cumsum.drop(columns=["person_count"], inplace=True)
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_cumsum)
        df_daily_cumsum = (
            df_daily_cumsum.set_index("date")
            .reindex(full_date_range)
            .fillna(method="ffill")
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_cumsum["indic_name"] = self.version + "-人数-累计值"
        df_daily_cumsum.fillna(0, inplace=True)

        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['date', 'area_name']).agg({'person_count': 'sum'}).reset_index()
        # 将 'date' 转换为 datetime 类型
        data_district['date'] = pd.to_datetime(data_district['date'])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area.unique()
        df_combinations = pd.MultiIndex.from_product([full_date_range, area_names],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = pd.to_datetime(df_full_combinations['date'].dt.date)

        data_district = sqldf(
            "select  a.date,a.area_name, ifnull(b.person_count,0) person_count from df_full_combinations a left join  data_district b on a.area_name = b.area_name and a.date = b.date")
        # 按照 'date' 排序
        data_district = data_district.sort_values(by='date')
        data_district['cumulative_count'] = data_district.groupby(['area_name'])['person_count'].cumsum()
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-人数-' + x + '-累计值')
        # 合并数据
        df_daily_cumsum = pd.concat([df_daily_cumsum, data_district[['date', 'cumulative_count', 'indic_name']]])


        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum["code"] = None
        # 获取code
        df_daily_cumsum = query_indicator_code_v1(df_daily_cumsum)
        # 删除code为空的行
        df_daily_cumsum.dropna(subset=["code"], inplace=True)
        df_daily_cumsum.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["cumulative_count"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_daily_person_cumsum error:{}".format(e))
        return df_daily_cumsum

    def get_daily_person_count(self):
        """
        获取日度参保人数，时间序列，用于更新历史数据
        """
        data = self.get_from_cache("get_daily_sale")
        df_daily_count = (
            data.groupby(["date"]).agg({"person_count": "sum"}).reset_index()
        )
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_count)
        df_daily_count = (
            df_daily_count.set_index("date")
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_count["indic_name"] = self.version + "-人数-当期值"

        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['date', 'area_name']).agg({'person_count': 'sum'}).reset_index()
        # 将 'date' 转换为 datetime 类型
        data_district['date'] = pd.to_datetime(data_district['date'])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area.unique()
        df_combinations = pd.MultiIndex.from_product([full_date_range, area_names],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = pd.to_datetime(df_full_combinations['date'].dt.date)

        data_district = sqldf(
            "select  a.date,a.area_name, ifnull(b.person_count,0) person_count from df_full_combinations a left join  data_district b on a.area_name = b.area_name and a.date = b.date")
        # 定义指标名称
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-人数-' + x + '-当期值')

        # 合并数据
        df_daily_count = pd.concat([df_daily_count, data_district[['date', 'person_count', 'indic_name']]])
        df_daily_count.reset_index(drop=True, inplace=True)
        df_daily_count["code"] = None

        # 获取code
        df_daily_count = query_indicator_code_v1(df_daily_count)
        # 删除code为空的行
        df_daily_count.dropna(subset=["code"], inplace=True)
        df_daily_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_count.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["person_count"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_daily_person_count error:{}".format(e))
        return df_daily_count

    def get_total_cumsum(self):
        """
        获取总销量
        """
        data = self.get_from_cache("get_daily_sale")
        total_count = data["count"].sum()
        indic_name = self.version + "-销量-累计值"
        df_total_cumsum = pd.DataFrame(
            {"indic_name": [indic_name], "count": [total_count]}
        )

        # 分区域销量累计值
        data_district = data.groupby(['area_name']).agg({'count': 'sum'}).reset_index()
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['area_name']).agg({'count': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.count from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-累计值')
        df_total_cumsum = pd.concat([df_total_cumsum, data_district[['indic_name', 'count']]])

        df_total_cumsum.reset_index(drop=True, inplace=True)
        df_total_cumsum["code"] = None
        # 获取code
        query_indicator_code(df_total_cumsum)
        # 删除code为空的行
        df_total_cumsum.dropna(subset=["code"], inplace=True)
        df_total_cumsum.fillna(0, inplace=True)
        # 写入数据库
        try:
            with transaction.atomic():
                for index, row in df_total_cumsum.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.end_time,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["count"])),
                        },
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_total_cumsum error:{}".format(e))
        return df_total_cumsum

    def get_today_count(self):
        """
        获取当日销量
        """
        data = self.get_from_cache("get_daily_sale")
        today_count = data[data["date"] == self.today.date()]["count"].sum()
        indic_name = self.version + "-销量-当期值"
        df_today_count = pd.DataFrame(
            {"indic_name": [indic_name], "count": [today_count]}
        )

        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data[data['date'] == self.today.date()].groupby(['area_name']).agg(
            {'count': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.count from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-当期值')
        df_today_count = pd.concat([df_today_count, data_district[['indic_name', 'count']]])

        df_today_count.reset_index(drop=True, inplace=True)
        df_today_count["code"] = None

        # 获取code
        query_indicator_code(df_today_count)
        # 删除code为空的行
        df_today_count.dropna(subset=["code"], inplace=True)
        df_today_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_today_count.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.end_time,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["count"])),
                        },
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_today_count error:{}".format(e))
        return df_today_count

    def get_yesterday_count(self):
        """
        获取昨日销量
        """
        # 检查 self.yesterday 是否小于 self.sale_start_date
        if self.yesterday.date() < self.sale_start_date:
            # 如果是，则返回空
            return None

        indic_name = self.version + "-销量-当期值"
        data = self.get_from_cache("get_daily_sale")
        data.fillna({"area_name": "其他"}, inplace=True)
        data["date"] = pd.to_datetime(data["date"])
        yesterday_count = data[data["date"] == self.yesterday]["count"].sum()
        df_yesterday_count = pd.DataFrame(
            {"indic_name": [indic_name], "count": [yesterday_count]}
        )

        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data[data['date'] == self.yesterday].groupby(['area_name']).agg(
            {'count': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.count from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-当期值')
        df_yesterday_count = pd.concat([df_yesterday_count, data_district[['indic_name', 'count']]])


        df_yesterday_count.reset_index(drop=True, inplace=True)
        df_yesterday_count["code"] = None

        # 获取code
        query_indicator_code(df_yesterday_count)

        # 删除code为空的行
        df_yesterday_count.dropna(subset=["code"], inplace=True)
        df_yesterday_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_yesterday_count.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.yesterday,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["count"])),
                        },
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_yesterday_count error:{}".format(e))
        return df_yesterday_count

    def get_daily_cumsum(self):
        """
        获取日度累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache("get_daily_sale")
        df_daily_cumsum = data.groupby(["date"]).agg({"count": "sum"}).reset_index()
        df_daily_cumsum.sort_values(by="date", ascending=True, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum["cumulative_count"] = df_daily_cumsum["count"].cumsum()
        df_daily_cumsum.drop(columns=["count"], inplace=True)
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_cumsum)
        df_daily_cumsum = (
            df_daily_cumsum.set_index("date")
            .reindex(full_date_range)
            .fillna(method="ffill")
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_cumsum.fillna(0, inplace=True)

        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['date', 'area_name']).agg({'count': 'sum'}).reset_index()
        # 将 'date' 转换为 datetime 类型
        data_district['date'] = pd.to_datetime(data_district['date'])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area.unique()
        df_combinations = pd.MultiIndex.from_product([full_date_range, area_names],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = pd.to_datetime(df_full_combinations['date'].dt.date)

        data_district = sqldf(
            "select  a.date,a.area_name, ifnull(b.count,0) count from df_full_combinations a left join  data_district b on a.area_name = b.area_name and a.date = b.date")
        # 按照 'date' 排序
        data_district = data_district.sort_values(by='date')
        data_district['cumulative_count'] = data_district.groupby(['area_name'])['count'].cumsum()
        # 定义指标名称
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-累计值')
        df_daily_cumsum['indic_name'] = self.version + '-销量-累计值'
        # 合并数据
        df_daily_cumsum = pd.concat([df_daily_cumsum, data_district[['date', 'cumulative_count', 'indic_name']]])
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum["code"] = None
        # 获取code
        df_daily_cumsum = query_indicator_code_v1(df_daily_cumsum)
        # 删除code为空的行
        df_daily_cumsum.dropna(subset=["code"], inplace=True)
        df_daily_cumsum.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["cumulative_count"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_daily_cumsum error:{}".format(e))
        return df_daily_cumsum

    def get_daily_count(self):
        """
        获取日度销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache("get_daily_sale")
        df_daily_count = data.groupby(["date"]).agg({"count": "sum"}).reset_index()
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_count)
        df_daily_count = (
            df_daily_count.set_index("date")
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_count["indic_name"] = self.version + "-销量-当期值"

        # 分区域当日销量
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['date', 'area_name']).agg({'count': 'sum'}).reset_index()
        # 将 'date' 转换为 datetime 类型
        data_district['date'] = pd.to_datetime(data_district['date'])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area.unique()
        df_combinations = pd.MultiIndex.from_product([full_date_range, area_names],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = pd.to_datetime(df_full_combinations['date'].dt.date)

        data_district = sqldf(
            "select  a.date,a.area_name, ifnull(b.count,0) count from df_full_combinations a left join  data_district b on a.area_name = b.area_name and a.date = b.date")
        # 定义指标名称
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-当期值')

        # 合并数据
        df_daily_count = pd.concat([df_daily_count, data_district[['date', 'count', 'indic_name']]])
        df_daily_count.reset_index(drop=True, inplace=True)
        df_daily_count["code"] = None

        # 获取code
        df_daily_count = query_indicator_code_v1(df_daily_count)
        # 删除code为空的行
        df_daily_count.dropna(subset=["code"], inplace=True)
        df_daily_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_count.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["count"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_daily_count error:{}".format(e))
        return df_daily_count

    def get_total_amount(self):
        """
        获取总销售额，即累计保费
        """
        data = self.get_from_cache("get_daily_sale")
        total_amount = str(data["amount"].sum())
        indic_name = self.version + "-销售额-累计值"
        df_total_amount = pd.DataFrame(
            {"indic_name": [indic_name], "amount": [total_amount]}
        )

        # 分区域销售额累计值
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['area_name']).agg({'amount': 'sum'}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, b.amount from df_area a left join  data_district b on a.name = b.area_name")
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销售额-' + x + '-累计值')
        df_total_amount = pd.concat([df_total_amount, data_district[['indic_name', 'amount']]])
        df_total_amount.reset_index(drop=True, inplace=True)
        df_total_amount["code"] = None
        # 获取code
        query_indicator_code(df_total_amount)
        # 删除code为空的行
        df_total_amount.dropna(subset=["code"], inplace=True)
        df_total_amount.fillna(0, inplace=True)
        # 写入数据库
        try:
            with transaction.atomic():
                for index, row in df_total_amount.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.end_time,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["amount"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_total_amount error:{}".format(e))
        return df_total_amount

    def get_daily_amount(self):
        """
        获取日度销售额，时间序列，用于更新历史数据
        """
        data = self.get_from_cache("get_daily_sale")
        df_daily_amount = data.groupby(["date"]).agg({"amount": "sum"}).reset_index()
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_amount)
        df_daily_amount = (
            df_daily_amount.set_index("date")
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_amount["indic_name"] = self.version + "-销售额-当期值"

        # 分区域日度销售额
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['date', 'area_name']).agg({'amount': 'sum'}).reset_index()
        # 将 'date' 转换为 datetime 类型
        data_district['date'] = pd.to_datetime(data_district['date'])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area.unique()
        df_combinations = pd.MultiIndex.from_product([full_date_range, area_names],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = pd.to_datetime(df_full_combinations['date'].dt.date)

        data_district = sqldf(
            "select  a.date,a.area_name, ifnull(b.amount,0) amount from df_full_combinations a left join  data_district b on a.area_name = b.area_name and a.date = b.date")
        # 定义指标名称
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销售额-' + x + '-当期值')

        # 合并数据
        df_daily_amount = pd.concat([df_daily_amount, data_district[['date', 'amount', 'indic_name']]])
        df_daily_amount.reset_index(drop=True, inplace=True)
        df_daily_amount["code"] = None

        # 获取code
        df_daily_amount = query_indicator_code_v1(df_daily_amount)
        # 删除code为空的行
        df_daily_amount.dropna(subset=["code"], inplace=True)
        df_daily_amount.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_amount.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["amount"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("get_daily_amount error:{}".format(e))
        return df_daily_amount

    def get_daily_amount_cumsum(self):
        """
        获取日度销售额累计值，时间序列，用于更新历史数据
        """
        data = self.get_from_cache("get_daily_sale")
        df_daily_amount_cumsum = (
            data.groupby(["date"]).agg({"amount": "sum"}).reset_index()
        )
        df_daily_amount_cumsum.sort_values(by="date", ascending=True, inplace=True)
        df_daily_amount_cumsum.reset_index(drop=True, inplace=True)
        df_daily_amount_cumsum["cumulative_amount"] = df_daily_amount_cumsum[
            "amount"
        ].cumsum()
        df_daily_amount_cumsum.drop(columns=["amount"], inplace=True)
        full_date_range = self.get_full_range_date(df_daily_amount_cumsum)
        df_daily_amount_cumsum = (
            df_daily_amount_cumsum.set_index("date")
            .reindex(full_date_range)
            .fillna(method="ffill")
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_amount_cumsum.fillna(0, inplace=True)
        df_daily_amount_cumsum["indic_name"] = self.version + "-销售额-累计值"

        # 分区域日度销售额累计值
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                'name']
        data_district = data.groupby(['date', 'area_name']).agg({'amount': 'sum'}).reset_index()
        # 将 'date' 转换为 datetime 类型
        data_district['date'] = pd.to_datetime(data_district['date'])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area.unique()
        df_combinations = pd.MultiIndex.from_product([full_date_range, area_names],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = pd.to_datetime(df_full_combinations['date'].dt.date)

        data_district = sqldf(
            "select  a.date,a.area_name, ifnull(b.amount,0) amount from df_full_combinations a left join "
            " data_district b on a.area_name = b.area_name and a.date = b.date")
        # 按照 'date' 排序
        data_district = data_district.sort_values(by='date')
        data_district['cumulative_amount'] = data_district.groupby(['area_name'])['amount'].cumsum()
        # 定义指标名称
        data_district['indic_name'] = data_district['area_name'].apply(
            lambda x: self.version + '-销售额-' + x + '-累计值')
        # 合并数据
        df_daily_amount_cumsum = pd.concat(
            [df_daily_amount_cumsum, data_district[['date', 'cumulative_amount', 'indic_name']]])
        df_daily_amount_cumsum.reset_index(drop=True, inplace=True)
        df_daily_amount_cumsum["code"] = None
        # 获取code
        df_daily_amount_cumsum = query_indicator_code_v1(df_daily_amount_cumsum)
        # 删除code为空的行
        df_daily_amount_cumsum.dropna(subset=["code"], inplace=True)
        df_daily_amount_cumsum.fillna(0, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_daily_amount_cumsum.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["cumulative_amount"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("get_daily_amount_cumsum error:{}".format(e))
        return df_daily_amount_cumsum

    def get_coverage_rate(self):
        """
        参保率统计
        数据分总体销量和各个区域的参保率
        """
        target_records = PublicTarget.objects.filter(
            product_set_code=self.product_set_code, type="product"
        ).order_by("update_time")
        df_target = pd.DataFrame(list(target_records.values()))
        total_target = df_target["target"].sum()
        data = self.get_from_cache("get_daily_sale")
        total_count = data["count"].sum()
        if total_target == 0:
            coverage_rate = 0
        else:
            coverage_rate = round(total_count / total_target * 100, 4)
        indic_name = self.version + "-参保率-当期值"
        df_coverage_rate = pd.DataFrame(
            [{"indic_name": indic_name, "coverage_rate": coverage_rate}]
        )

        # 分区域参保率
        df_area = pd.DataFrame(
            list(
                PublicAreaBaseInsure.objects.filter(
                    product_set_code=self.product_set_code
                ).values()
            )
        )[["name", "target"]]
        data_district = data.groupby(["area_name"]).agg({"count": "sum"}).reset_index()
        data_district = sqldf(
            "select a.name as area_name, case when a.target =0 then 0 else round(ifnull(b.count,0)/a.target*100,4) end as coverage_rate "
            " from df_area a left join  data_district b on a.name = b.area_name"
        )
        data_district["indic_name"] = data_district["area_name"].apply(
            lambda x: self.version + "-参保率-" + x + "-当期值"
        )
        df_coverage_rate = pd.concat(
            [df_coverage_rate, data_district[["indic_name", "coverage_rate"]]]
        )
        df_coverage_rate.reset_index(drop=True, inplace=True)
        df_coverage_rate["code"] = None

        # 获取code
        query_indicator_code(df_coverage_rate)
        # 删除code为空的行
        df_coverage_rate.dropna(subset=["code"], inplace=True)
        df_coverage_rate.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_coverage_rate.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=self.end_time,
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["coverage_rate"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_coverage_rate error:{}".format(e))
        return df_coverage_rate

    def get_daily_coverage_rate(self):
        """
        日度参保率统计，时间序列，用于更新历史数据
        数据分总体销量和各个区域的销量
        """
        target_records = PublicTarget.objects.filter(
            product_set_code=self.product_set_code, type="product"
        ).order_by("update_time")
        df_target = pd.DataFrame(list(target_records.values()))
        total_target = df_target["target"].sum()
        data = self.get_from_cache("get_daily_sale")
        
        df_daily_count = data.groupby(["date"]).agg({"count": "sum"}).reset_index()
        
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_count)
        df_daily_coverage_rate = (
            df_daily_count.set_index("date")
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={"index": "date"})
        )
        df_daily_coverage_rate["cumulative_count"] = df_daily_coverage_rate["count"].cumsum()
        if total_target == 0:
            df_daily_coverage_rate["coverage_rate"] = 0
        else:
            df_daily_coverage_rate["coverage_rate"] = round(
                df_daily_coverage_rate["cumulative_count"] / total_target * 100, 4
            )
        df_daily_coverage_rate["indic_name"] = self.version + "-参保率-当期值"
        print(df_daily_coverage_rate.tail(30))
        # 分区域当日销量
        df_area = pd.DataFrame(
            list(
                PublicAreaBaseInsure.objects.filter(
                    product_set_code=self.product_set_code
                ).values()
            )
        )[["name", "target"]]
        
        data_district = (
            data.groupby(["date", "area_name"]).agg({"count": "sum"}).reset_index()
        )
        
        # 将 'date' 转换为 datetime 类型
        data_district["date"] = pd.to_datetime(data_district["date"])
        # 创建一个完整的日期与地区名称的组合 DataFrame
        area_names = df_area["name"].unique()
        df_combinations = pd.MultiIndex.from_product(
            [full_date_range, area_names], names=["date", "area_name"]
        )
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations["date"] = pd.to_datetime(df_full_combinations["date"].dt.date)
        df_full_combinations = sqldf(
            "select a.*,b.target from df_full_combinations a left join df_area b on a.area_name = b.name"
        )
        data_district = sqldf(
            "select a.date,a.area_name,ifnull(b.count,0) count from df_full_combinations a left join data_district b on a.area_name = b.area_name and a.date = b.date"
        )

        data_district['cumulative_count'] = data_district.groupby(['area_name'])['count'].cumsum()
        print(data_district.tail(10))
        # 确保使用浮点数除法
        data_district = sqldf(
            "select  a.date,a.area_name,a.target,b.cumulative_count, "
            "case when a.target =0 then 0 else round(b.cumulative_count*100.0/a.target,4) end as coverage_rate "
            "from df_full_combinations a left join data_district b "
            "on a.area_name = b.area_name and a.date = b.date"
        )
        # 定义指标名称
        data_district["indic_name"] = data_district["area_name"].apply(
            lambda x: self.version + "-参保率-" + x + "-当期值"
        )
        print(data_district.tail(30))
        # 合并数据
        df_daily_coverage_rate = pd.concat(
            [
                df_daily_coverage_rate,
                data_district[["date", "coverage_rate", "indic_name"]],
            ]
        )
        df_daily_coverage_rate.reset_index(drop=True, inplace=True)
        df_daily_coverage_rate["code"] = None

        # 获取code
        df_daily_coverage_rate = query_indicator_code_v1(df_daily_coverage_rate)
        print(df_daily_coverage_rate.tail(10))
        # 删除code为空的行
        df_daily_coverage_rate.dropna(subset=["code"], inplace=True)
        df_daily_coverage_rate.fillna(0, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_daily_coverage_rate.iterrows():
                    custom_update_or_create(
                        PublicIndicatorData,
                        code=row["code"],
                        end_time=row["date"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["coverage_rate"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_daily_coverage_rate error:{}".format(e))
        return df_daily_coverage_rate

    def get_product_count(self):
        """
        不同产品参保人数统计,基础惠学版、升级乐学版、至尊优学版
        """
        df_all_product = self.get_from_cache("get_all_product")
        data = self.get_from_cache("get_daily_sale")
        df_product_count = (
            data.groupby(["product_name"]).agg({"person_count": "sum"}).reset_index()
        )
        # 保证每个产品都有数据
        df_product_coverage_rate = sqldf(
            "select p.name product_name,ifnull(a.person_count,0) person_count from df_all_product p left join df_product_count a "
            " on p.name = a.product_name"
        )
        try:
            with transaction.atomic():
                for index, row in df_product_coverage_rate.iterrows():
                    custom_update_or_create(
                        PublicStatistics,
                        type=self.type,
                        statistical_type="product_person",
                        product_set_code=self.product_set_code,
                        key=row["product_name"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["person_count"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_product_count error:{}".format(e))


        # 分区域参保率
        df_area = pd.DataFrame(
            list(
                PublicAreaBaseInsure.objects.filter(
                    product_set_code=self.product_set_code
                ).values()
            )
        )[["name", "target"]]
        data_district = data.groupby(["area_name","product_name"]).agg({"person_count": "sum"}).reset_index()
        area_names = df_area["name"].unique()
        all_product = df_all_product["name"].unique()
        df_combinations = pd.MultiIndex.from_product(
            [area_names,all_product], names=["area_name" , "product_name"]
        )
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_data_district = sqldf(
            "select a.area_name,a.product_name,ifnull(b.person_count,0) person_count from df_full_combinations a left join data_district b "
            " on a.area_name = b.area_name and a.product_name = b.product_name"
        )
        try:
            with transaction.atomic():
                for index, row in df_data_district.iterrows():
                    custom_update_or_create(
                        PublicStatistics,
                        type=self.type,
                        statistical_type="product_person",
                        product_set_code=self.product_set_code,
                        key=row["product_name"],
                        additional_info=row['area_name'],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": Decimal(str(row["person_count"])),
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_product_count area error:{}".format(e))

        return df_product_coverage_rate,df_data_district

    def get_school_count(self):
        """
        学校销量统计
        """
        data = self.get_from_cache("get_daily_sale")
        # 每个区都有同名的学校，所以加上区域名称
        df_pay_type_count = (
            data.groupby(["area_name", "school_name"])
            .agg({"person_count": "sum"})
            .reset_index()
        )
        df_pay_type_count["person_count"] = df_pay_type_count["person_count"].apply(
            lambda x: Decimal(str(x))
        )
        # 获取学校数据
        df_school = SystemDictValue.objects.filter(
            dict_id=11, description=self.city, status=1
        )
        df_school = pd.DataFrame(list(df_school.values()))[["key", "label"]].rename(
            columns={"key": "school_name", "label": "area_name"}
        )

        # 合并数据，防止开始没有成单，接口有问题
        df_pay_type_count = pd.merge(
            df_school, df_pay_type_count, on=["school_name", "area_name"], how="left"
        )
        df_pay_type_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_pay_type_count.iterrows():
                    custom_update_or_create(
                        PublicStatistics,
                        type=self.type,
                        statistical_type="school",
                        product_set_code=self.product_set_code,
                        key=row["school_name"],
                        additional_info=row["area_name"],
                        defaults={
                            "publish_time": self.publish_time,
                            "value": row["person_count"],
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_school_count error:{}".format(e))
        return df_pay_type_count

    def get_area_info(self):
        """
        地区参保数据，由于学校一定会有地区，所以不在设置其他项
        """
        df_area = PublicAreaBaseInsure.objects.filter(
            product_set_code=self.product_set_code
        )
        df_area = pd.DataFrame(list(df_area.values()))[
            ["target", "code", "name"]
        ].rename(
            columns={"code": "area_code", "target": "base_insure", "name": "area_name"}
        )
        data = self.get_from_cache("get_daily_sale")
        # code部分超过6位，取前6位
        data["area_code"] = data["area_code"].apply(
            lambda x: x[:6] if x is not None else x
        )
        # area_code为空的置为999999，area_name为空的置为'其他'
        data.fillna({"area_code": "999999", "area_name": "其他"}, inplace=True)
        # 销售量按照人数统计
        df_area_info = (
            data.groupby(["area_name", "date"])
            .agg({"person_count": "sum"})
            .reset_index()
        )
        df_area_info.rename(columns={"person_count": "count"}, inplace=True)
        df_area_total = (
            df_area_info.groupby(["area_name"]).agg({"count": "sum"}).reset_index()
        )
        df_area_total = pd.merge(df_area_total, df_area, how="outer", on="area_name")
        # 如果name为其他，area_code置为999999
        df_area_total.loc[df_area_total["area_name"] == "其他", "area_code"] = "999999"
        df_area_total.fillna(0, inplace=True)
        df_area_total.sort_values(by="count", ascending=False, inplace=True)
        total_count = df_area_total["count"].sum()
        df_area_total["ratio"] = df_area_total.apply(
            lambda row: round(row["count"] / total_count, 3) if total_count != 0 else 0,
            axis=1,
        )
        df_area_total["insure_ratio"] = df_area_total.apply(
            lambda row: (
                round(row["count"] / row["base_insure"], 3)
                if row["base_insure"] != 0
                else 0
            ),
            axis=1,
        )
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total["position"] = df_area_total.index + 1
        # name如果为其他，放到最后，调整索引
        df_area_total.loc[df_area_total["area_name"] == "其他", "position"] = (
            df_area_total.shape[0] + 1
        )
        df_area_total.sort_values(by="position", inplace=True)
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total["position"] = df_area_total.index + 1
        df_area_total["count"] = df_area_total["count"].astype(int)
        # 统计今日、昨日销量
        df_area_today = (
            df_area_info[df_area_info["date"] == self.today.date()]
            .groupby(["area_name"])
            .agg({"count": "sum"})
            .reset_index()
            .rename(columns={"count": "today_count"})
        )
        df_area_yesterday = (
            df_area_info[df_area_info["date"] == self.yesterday.date()]
            .groupby(["area_name"])
            .agg({"count": "sum"})
            .reset_index()
            .rename(columns={"count": "yesterday_count"})
        )
        if df_area_today.empty:
            df_area_total["today_count"] = 0
        else:
            df_area_total = pd.merge(
                df_area_total, df_area_today, how="left", on="area_name"
            )
            df_area_total["today_count"].fillna(0, inplace=True)
        if df_area_yesterday.empty:
            df_area_total["yesterday_count"] = 0
        else:
            df_area_total = pd.merge(
                df_area_total, df_area_yesterday, how="left", on="area_name"
            )
            df_area_total["yesterday_count"].fillna(0, inplace=True)
        number_sum = (
            df_area_total.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        )
        number_sum["ratio"] = number_sum["ratio"].apply(lambda x: 1 if x > 1 else x)
        number_sum["position"] = len(df_area_total) + 1
        number_sum["insure_ratio"] = number_sum.apply(
            lambda row: (
                0
                if row["base_insure"] == 0
                else np.round(row["count"] / row["base_insure"], 3)
            ),
            axis=1,
        )
        df_area_total = pd.concat([df_area_total, number_sum], axis=0).reset_index(
            drop=True
        )
        df_area_total["product_set_code"] = self.product_set_code
        df_area_total["publish_time"] = self.publish_time
        df_area_total["ratio"] = df_area_total["ratio"].replace({np.nan: None})
        df_area_total["insure_ratio"] = df_area_total["insure_ratio"].replace(
            {np.nan: None}
        )
        # 查询数据库中所有数据
        db_insure_area = InsureArea.objects.filter(
            product_set_code=self.product_set_code
        )
        if db_insure_area.exists():
            df_db_insure_area = pd.DataFrame(list(db_insure_area.values()))[
                ["id", "name"]
            ]
        else:
            df_db_insure_area = pd.DataFrame(columns=["id", "name"])

        delete_df = sqldf(
            "select a.id from df_db_insure_area a left join df_area_total b on a.name = b.area_name where b.area_name is null"
        )
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df["id"].tolist()
            print(delete_ids)
            InsureArea.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_area_total.iterrows():
                    custom_update_or_create(
                        InsureArea,
                        product_set_code=row["product_set_code"],
                        name=row["area_name"],
                        defaults={
                            "publish_time": self.publish_time,
                            "total_count": row["count"],
                            "ratio": row["ratio"],
                            "insure_ratio": row["insure_ratio"],
                            "position": row["position"],
                            "today_count": row["today_count"],
                            "yesterday_count": row["yesterday_count"],
                        },
                        exclude_fields=["publish_time"],
                    )
        except Exception as e:
            logger.error("BzHxbInsureV3 get_area_info error:{}".format(e))
        return df_area_total

    def get_area_age_gender_count(self):
        """
        分地区年龄性别统计
        """
        with self.get_connection() as conn:
            df_age_gender_count = pd.read_sql(
                query_sql("SQL_AREA_AGE_GENDER").format(
                    product_set_code=self.product_set_code,
                    start_datetime=self.sale_start_time,
                    end_datetime=self.publish_time,
                ),
                conn,
            )
        return df_age_gender_count

    def cache_area_age_gender_count(self):
        """
        缓存分地区年龄性别数据，主动推送，保证数据的实时性
        """
        try:
            _start = time.time()
            df_age_gender_count = self.get_area_age_gender_count()
            self.update_cache("get_area_age_gender_count", df_age_gender_count)
            _cost = int((time.time() - _start) * 1000)
            logger.info(
                "BzHxbInsureV3 cache_area_age_gender_count cost:{}ms".format(_cost)
            )
            return df_age_gender_count
        except Exception as e:
            logger.error("BzHxbInsureV3 cache_area_age_gender_count error:{}".format(e))
            raise ValueError(
                "BzHxbInsureV3 cache_area_age_gender_count error:{}".format(e)
            )

    def get_age_gender(self):
        """
        年龄性别统计
        分总量数据和分地区数据
        """
        df_age_gender = self.get_from_cache("get_area_age_gender_count")
        print(df_age_gender)
        age_bins = [3, 6, 12, 18, float("inf")]
        age_labels = ["3-6岁", "7-11岁", "12-17岁", "18-25岁"]
        age_group(df_age_gender, "age", age_bins, age_labels)
        df_age_gender["gender"].replace({"FEMALE": "女", "MALE": "男"}, inplace=True)
        df_age_gender_group = (
            df_age_gender.groupby(["age_group", "gender"])
            .agg({"person_count": "sum"})
            .reset_index()
        )
        # 如果组合在df_age_gender_group中不存在，则增加一条，值为0，保证数据完整
        for x in age_labels:
            for s in ["男", "女"]:
                if (
                    len(
                        df_age_gender_group[
                            (df_age_gender_group["age_group"] == x)
                            & (df_age_gender_group["gender"] == s)
                        ]
                    )
                    == 0
                ):
                    df_age_gender_group = df_age_gender_group._append(
                        {"age_group": x, "gender": s, "person_count": 0},
                        ignore_index=True,
                    )

        df_age_gender_group.sort_values(by=["age_group", "gender"], inplace=True)
        df_age_gender_group.reset_index(drop=True, inplace=True)
        df_age_gender_group["additional_info"] = None
        df_age_gender_group["product_set_code"] = self.product_set_code
        df_age_gender_group["publish_time"] = self.publish_time
        
        # 分区域分年龄性别总量范围
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                ['name', 'target']]
        area_age_gender_combinations = pd.MultiIndex.from_product(
            [df_area['name'].unique(), age_labels, ['男', '女']],
            names=['area_name', 'age_group', 'gender'])

        df_area_age_gender_combinations = pd.DataFrame(index=area_age_gender_combinations).reset_index()
        df_area_age_gender_combinations = sqldf(
            "select a.*,ifnull(b.person_count,0) as 'person_count' from df_area_age_gender_combinations a left join df_age_gender b on a.age_group = b.age_group and a.gender = b.gender and a.area_name = b.area_name")
        df_area_age_gender_combinations = df_area_age_gender_combinations.groupby(['area_name','age_group','gender']).agg(
            {'person_count': 'sum'}).reset_index()
        df_area_age_gender_combinations.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_age_gender_group['additional_info'] = None
        df_age_gender_group = pd.concat([df_age_gender_group, df_area_age_gender_combinations], axis=0).reset_index(
            drop=True)
        df_age_gender_group.sort_values(by=['age_group', 'gender'], inplace=True)
        df_age_gender_group.reset_index(drop=True, inplace=True)
        df_age_gender_group['product_set_code'] = self.product_set_code
        df_age_gender_group['publish_time'] = self.publish_time


        age_mapping = {age: i for i, age in enumerate(age_labels)}
        df_age_gender_group['age_order'] = df_age_gender_group['age_group'].map(age_mapping)
        df_age_gender_group = df_age_gender_group.sort_values(by=['additional_info', 'age_order'])
        df_age_gender_group = df_age_gender_group.drop('age_order', axis=1).reset_index(drop=True)
        try:
            with transaction.atomic():
                for index, row in df_age_gender_group.iterrows():
                    custom_update_or_create(InsureAgeSex,
                                            product_set_code=row['product_set_code'], sex=row['gender'],
                                            age_distribution=row['age_group'], additional_info=row['additional_info'],
                                            defaults={'value': row['person_count'],
                                                      'publish_time': row['publish_time']},
                                            exclude_fields=['publish_time'])
                    
        except Exception as e:
            logger.error("BzHxbInsureV3 get_age_gender error:{}".format(e))
        return df_age_gender_group

    def get_pay_type_count(self):
        """
        支付方式人数统计
        """
        data = self.get_from_cache("get_daily_sale")
        df_pay_type_count = (
            data.groupby(["pay_type"]).agg({"person_count": "sum"}).reset_index()
        )
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(
            df_pay_type_count,
            "pay_type",
            {"NORMAL": "常规支付", "MEDICARE": "个账支付"},
        )
        df_pay = pd.DataFrame({"pay_type": ["常规支付", "个账支付"]})
        df_pay_type_count = pd.merge(
            df_pay, df_pay_type_count, how="left", on="pay_type"
        )
        df_pay_type_count.fillna(0, inplace=True)
        # 分区域分支付方式总量范围
        df_area = \
            pd.DataFrame(list(PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code).values()))[
                ['name', 'target']]
        area_paytype_combinations = pd.MultiIndex.from_product(
            [df_area['name'].unique(), ['常规支付', '个账支付']],
            names=['area_name', 'pay_type'])
        df_area_paytype_combinations = pd.DataFrame(index=area_paytype_combinations).reset_index()
        df_area_pay_type_count = data.groupby(['area_name', 'pay_type']).agg({'person_count': 'sum'}).reset_index()
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(df_area_pay_type_count, 'pay_type', {'NORMAL': '常规支付', 'MEDICARE': '个账支付'})
        df_area_pay_type_count = sqldf(
            "select a.*,ifnull(b.person_count,0) as 'person_count' from df_area_paytype_combinations a left join df_area_pay_type_count b on a.pay_type = b.pay_type and a.area_name = b.area_name")
        df_area_pay_type_count.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_pay_type_count['additional_info'] = None
        df_pay_type_count = pd.concat([df_pay_type_count, df_area_pay_type_count], axis=0).reset_index(drop=True)
        try:
            with transaction.atomic():
                for index, row in df_pay_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay_person',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['person_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error("BzHxbInsureV3 get_pay_type_count error:{}".format(e))
        return df_pay_type_count

    def get_school(self):
        """
        获取学校所在地
        """
        data = self.get_from_cache("get_daily_sale")
        # 根据学校、产品分组统计销量
        df_school_total = data[["area_name", "school_name"]].drop_duplicates()
        df_school_total["description"] = self.city
        df_school_total.dropna(subset=["school_name"], inplace=True)
        df_school_total.reset_index(drop=True, inplace=True)
        # 学校列表数据写入中间表
        if df_school_total.shape[0] > 0:
            try:
                with transaction.atomic():
                    for index, row in df_school_total.iterrows():
                        custom_update_or_create(
                            SystemDictValue,
                            dict_id=11,
                            key=row["school_name"],
                            description=row["description"],
                            label=row["area_name"],
                            defaults={"status": 1},
                        )
            except Exception as e:
                logger.error("BzHxbInsureV3 get_school error:{}".format(e))

        return df_school_total


if __name__ == "__main__":
    bz = BzHxbInsureV3()
    bz.cache_daily_sale()
    df_total_person_cumsum = bz.get_total_person_cumsum()
    print(df_total_person_cumsum)
    df_today_person_count = bz.get_today_person_count()
    print(df_today_person_count)
    df_yesterday_person_count = bz.get_yesterday_person_count()
    print(df_yesterday_person_count)
    df_daily_person_cumsum = bz.get_daily_person_cumsum()
    print(df_daily_person_cumsum)
    df_daily_person_count = bz.get_daily_person_count()
    print(df_daily_person_count)
    total_count = bz.get_total_cumsum()
    print(total_count)
    today_count = bz.get_today_count()
    print(today_count)
    df_yesterday_count = bz.get_yesterday_count()
    print(df_yesterday_count)
    df_daily_cumsum = bz.get_daily_cumsum()
    print(df_daily_cumsum)
    df_daily_count = bz.get_daily_count()
    print(df_daily_count)
    total_amount = bz.get_total_amount()
    print(total_amount)
    df_daily_amount = bz.get_daily_amount()
    print(df_daily_amount)
    df_daily_amount_cumsum = bz.get_daily_amount_cumsum()
    print(df_daily_amount_cumsum)
    coverage_rate = bz.get_coverage_rate()
    print(coverage_rate)
    bz.cache_all_product()
    df_daily_coverage_rate = bz.get_daily_coverage_rate()
    print(df_daily_coverage_rate)
    df_product_count = bz.get_product_count()
    print(df_product_count)

    df_school_count = bz.get_school_count()
    print(df_school_count)
    df_area_info = bz.get_area_info()
    print(df_area_info)
    bz.cache_area_age_gender_count()
    df_age_gender_count = bz.get_area_age_gender_count()
    print(df_age_gender_count)
    df_age_gender = bz.get_age_gender()
    print(df_age_gender)
    df_pay_type_count = bz.get_pay_type_count()
    print(df_pay_type_count)
    df_school_location = bz.get_school()
    print(df_school_location)
